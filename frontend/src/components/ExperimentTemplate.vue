<template>
  <div class="experiment-template">
    <!-- 实验头部 -->
    <div class="experiment-header">
      <div class="header-content">
        <h1 class="experiment-title">{{ experimentInfo?.name }}</h1>
        <p class="experiment-description">{{ experimentInfo?.description }}</p>
        
        <!-- 实验信息标签 -->
        <div class="experiment-meta">
          <el-tag :type="difficultyType" size="small">
            {{ difficultyText }}
          </el-tag>
          <el-tag type="info" size="small">
            <el-icon><Clock /></el-icon>
            {{ experimentInfo?.duration }}分钟
          </el-tag>
          <el-tag 
            v-for="tag in experimentInfo?.tags" 
            :key="tag" 
            size="small"
            effect="plain"
          >
            {{ tag }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 进度条 -->
    <div class="progress-section">
      <el-steps 
        :active="currentStep - 1" 
        :space="200" 
        finish-status="success"
        align-center
      >
        <el-step 
          v-for="(step, index) in steps" 
          :key="step.id"
          :title="step.title"
          :description="step.description"
          @click="handleStepClick(index + 1)"
          :class="{ 'step-clickable': isStepAccessible(index + 1) }"
        />
      </el-steps>
      
      <div class="progress-bar">
        <el-progress 
          :percentage="progressPercentage" 
          :stroke-width="6"
          :show-text="false"
        />
        <span class="progress-text">
          {{ currentStep }} / {{ totalSteps }} 步骤 ({{ progressPercentage }}%)
        </span>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="experiment-content">
      <el-card class="step-card" shadow="hover">
        <!-- 步骤标题 -->
        <template #header>
          <div class="step-header">
            <div class="step-number">{{ currentStep }}</div>
            <div class="step-info">
              <h3>{{ currentStepConfig?.title }}</h3>
              <p v-if="currentStepConfig?.description">
                {{ currentStepConfig.description }}
              </p>
            </div>
            
            <!-- 提示按钮 -->
            <el-button 
              v-if="currentStepConfig?.hints?.length"
              type="info" 
              size="small" 
              :icon="QuestionFilled"
              @click="showHints = true"
            >
              提示
            </el-button>
          </div>
        </template>

        <!-- 动态步骤内容 -->
        <div class="step-content">
          <component 
            :is="currentStepComponent"
            v-if="currentStepConfig"
            :config="currentStepConfig.config"
            :data="stepData"
            @update="handleStepDataUpdate"
            @next="handleNext"
            @prev="handlePrev"
          />
        </div>
      </el-card>
    </div>

    <!-- 导航按钮 -->
    <div class="navigation-section">
      <div class="nav-buttons">
        <el-button 
          v-if="canGoPrev"
          size="large"
          @click="handlePrev"
          :icon="ArrowLeft"
        >
          上一步
        </el-button>
        
        <div class="nav-center">
          <el-button 
            type="info" 
            size="small"
            @click="handleSaveDraft"
            :loading="isSavingDraft"
            :icon="DocumentCopy"
          >
            保存草稿
          </el-button>
        </div>
        
        <el-button 
          v-if="canGoNext"
          type="primary"
          size="large"
          @click="handleNext"
          :icon="ArrowRight"
        >
          下一步
        </el-button>
        
        <el-button 
          v-else
          type="success"
          size="large"
          @click="handleSubmit"
          :loading="isSubmitting"
          :icon="Check"
        >
          提交实验
        </el-button>
      </div>
    </div>

    <!-- 提示对话框 -->
    <el-dialog 
      v-model="showHints" 
      title="实验提示" 
      width="500px"
      :show-close="true"
    >
      <div class="hints-content">
        <el-alert 
          v-for="(hint, index) in currentStepConfig?.hints" 
          :key="index"
          :title="`提示 ${index + 1}`"
          :description="hint"
          type="info"
          :closable="false"
          show-icon
          class="hint-item"
        />
      </div>
      
      <template #footer>
        <el-button @click="showHints = false">知道了</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { 
  Clock, 
  QuestionFilled, 
  ArrowLeft, 
  ArrowRight, 
  Check, 
  DocumentCopy 
} from '@element-plus/icons-vue'
import { useSteps } from '@/composables/useSteps'
import { useSubmission } from '@/composables/useSubmission'
import { useExperimentStore } from '@/stores/experiment'

// 组件引用映射
const componentMap = {
  'TheoryStep': () => import('./steps/TheoryStep.vue'),
  'DataInputStep': () => import('./steps/DataInputStep.vue'),
  'AnalysisStep': () => import('./steps/AnalysisStep.vue'),
  'SummaryStep': () => import('./steps/SummaryStep.vue'),
  'OscilloscopeOperationStep': () => import('./steps/OscilloscopeOperationStep.vue'),
  'DensityMeasurementStep': () => import('./steps/DensityMeasurementStep.vue'),
  'CalculationStep': () => import('./steps/CalculationStep.vue'),
  'MeasurementStep': () => import('./steps/MeasurementStep.vue')
}

const experimentStore = useExperimentStore()
const { 
  currentStep, 
  totalSteps, 
  canGoNext, 
  canGoPrev, 
  progressPercentage,
  currentStepConfig,
  nextStep,
  prevStep,
  goToStep,
  isStepAccessible
} = useSteps()

const { 
  isSubmitting, 
  submitExperiment, 
  saveDraft 
} = useSubmission()

// 响应式数据
const showHints = ref(false)
const isSavingDraft = ref(false)

// 计算属性
const experimentInfo = computed(() => experimentStore.currentExperiment?.info)
const steps = computed(() => experimentStore.currentExperiment?.steps || [])
const stepData = computed(() => {
  const stepId = currentStepConfig.value?.id
  return stepId ? experimentStore.experimentData[stepId] || {} : {}
})

const currentStepComponent = computed(() => {
  const componentName = currentStepConfig.value?.component
  return componentName && componentMap[componentName] 
    ? componentMap[componentName] 
    : null
})

const difficultyType = computed(() => {
  const difficulty = experimentInfo.value?.difficulty
  const typeMap = {
    'easy': 'success',
    'medium': 'warning', 
    'hard': 'danger'
  }
  return typeMap[difficulty] || 'info'
})

const difficultyText = computed(() => {
  const difficulty = experimentInfo.value?.difficulty
  const textMap = {
    'easy': '简单',
    'medium': '中等',
    'hard': '困难'
  }
  return textMap[difficulty] || '未知'
})

// 事件处理
const handleStepClick = (step: number) => {
  if (isStepAccessible(step)) {
    goToStep(step)
  }
}

const handleStepDataUpdate = (data: any) => {
  const stepId = currentStepConfig.value?.id
  if (stepId) {
    experimentStore.updateStepData(stepId, data)
  }
}

const handleNext = () => {
  nextStep()
}

const handlePrev = () => {
  prevStep()
}

const handleSubmit = async () => {
  await submitExperiment()
}

const handleSaveDraft = async () => {
  isSavingDraft.value = true
  try {
    await saveDraft()
  } finally {
    isSavingDraft.value = false
  }
}
</script>

<style lang="scss" scoped>
.experiment-template {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.experiment-header {
  margin-bottom: 30px;
  
  .header-content {
    text-align: center;
  }
  
  .experiment-title {
    font-size: 2rem;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin-bottom: 10px;
  }
  
  .experiment-description {
    font-size: 1.1rem;
    color: var(--el-text-color-regular);
    margin-bottom: 20px;
  }
  
  .experiment-meta {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
  }
}

.progress-section {
  margin-bottom: 30px;
  
  .progress-bar {
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    
    .progress-text {
      font-size: 0.9rem;
      color: var(--el-text-color-regular);
      white-space: nowrap;
    }
  }
}

.step-clickable {
  cursor: pointer;
  
  &:hover {
    opacity: 0.8;
  }
}

.experiment-content {
  margin-bottom: 30px;
  
  .step-card {
    min-height: 400px;
  }
  
  .step-header {
    display: flex;
    align-items: center;
    gap: 15px;
    
    .step-number {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: var(--el-color-primary);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 1.2rem;
    }
    
    .step-info {
      flex: 1;
      
      h3 {
        margin: 0 0 5px 0;
        font-size: 1.3rem;
        color: var(--el-text-color-primary);
      }
      
      p {
        margin: 0;
        color: var(--el-text-color-regular);
      }
    }
  }
  
  .step-content {
    padding: 20px 0;
  }
}

.navigation-section {
  .nav-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .nav-center {
      flex: 1;
      display: flex;
      justify-content: center;
    }
  }
}

.hints-content {
  .hint-item {
    margin-bottom: 15px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

@media (max-width: 768px) {
  .experiment-template {
    padding: 15px;
  }
  
  .progress-section {
    :deep(.el-steps) {
      .el-step__title {
        font-size: 12px;
      }
    }
  }
  
  .navigation-section {
    .nav-buttons {
      flex-direction: column;
      gap: 15px;
      
      .nav-center {
        order: -1;
      }
    }
  }
}
</style>
