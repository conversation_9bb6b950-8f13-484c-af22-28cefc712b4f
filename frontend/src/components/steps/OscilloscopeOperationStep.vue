<template>
  <div class="oscilloscope-operation-step">
    <div class="step-header">
      <h2><el-icon><Tools /></el-icon> {{ config.title || '示波器操作步骤' }}</h2>
      <p class="step-description">{{ config.description }}</p>
    </div>

    <!-- 子步骤导航 -->
    <div class="sub-steps-nav">
      <el-steps :active="currentSubStep" align-center>
        <el-step 
          v-for="(subStep, index) in config.subSteps" 
          :key="index"
          :title="subStep.title"
          :description="subStep.shortDesc"
          :status="getSubStepStatus(index)"
          @click="goToSubStep(index)"
        />
      </el-steps>
    </div>

    <!-- 当前子步骤内容 -->
    <div class="sub-step-content">
      <transition name="slide-fade" mode="out-in">
        <div :key="currentSubStep" class="sub-step-container">
          <div v-if="currentSubStepConfig" class="sub-step-detail">
            <h3>{{ currentSubStepConfig.title }}</h3>
            <p class="sub-step-desc">{{ currentSubStepConfig.description }}</p>

            <!-- 操作指导 -->
            <div v-if="currentSubStepConfig.instructions" class="instructions-section">
              <h4><el-icon><Guide /></el-icon> 操作步骤</h4>
              <ol class="instruction-list">
                <li 
                  v-for="(instruction, index) in currentSubStepConfig.instructions" 
                  :key="index"
                  class="instruction-item"
                  :class="{ 'completed': isInstructionCompleted(index) }"
                >
                  <div class="instruction-content">
                    <span class="instruction-text">{{ instruction.text }}</span>
                    <el-tag v-if="instruction.warning" type="warning" size="small">
                      {{ instruction.warning }}
                    </el-tag>
                  </div>
                  <div class="instruction-actions">
                    <el-button 
                      v-if="instruction.checkable"
                      :type="isInstructionCompleted(index) ? 'success' : 'primary'"
                      size="small"
                      @click="toggleInstructionComplete(index)"
                    >
                      <el-icon><Check v-if="isInstructionCompleted(index)" /><Plus v-else /></el-icon>
                      {{ isInstructionCompleted(index) ? '已完成' : '标记完成' }}
                    </el-button>
                  </div>
                </li>
              </ol>
            </div>

            <!-- 参数设置 -->
            <div v-if="currentSubStepConfig.parameters" class="parameters-section">
              <h4><el-icon><Setting /></el-icon> 参数设置</h4>
              <el-row :gutter="20">
                <el-col 
                  v-for="param in currentSubStepConfig.parameters" 
                  :key="param.name"
                  :span="param.span || 12"
                >
                  <el-form-item :label="param.label">
                    <el-input-number
                      v-if="param.type === 'number'"
                      v-model="parameterValues[param.name]"
                      :min="param.min"
                      :max="param.max"
                      :precision="param.precision"
                      :step="param.step"
                      @change="handleParameterChange(param.name, $event)"
                    />
                    <el-select
                      v-else-if="param.type === 'select'"
                      v-model="parameterValues[param.name]"
                      @change="handleParameterChange(param.name, $event)"
                    >
                      <el-option
                        v-for="option in param.options"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                    <span v-if="param.unit" class="parameter-unit">{{ param.unit }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 验证结果 -->
            <div v-if="currentSubStepConfig.validation" class="validation-section">
              <h4><el-icon><CircleCheck /></el-icon> 验证结果</h4>
              <div class="validation-content">
                <el-alert
                  v-for="(result, index) in validationResults"
                  :key="index"
                  :title="result.message"
                  :type="result.type"
                  :closable="false"
                  show-icon
                />
              </div>
            </div>

            <!-- 提示信息 -->
            <div v-if="currentSubStepConfig.hints" class="hints-section">
              <el-collapse>
                <el-collapse-item title="💡 操作提示" name="hints">
                  <ul class="hints-list">
                    <li v-for="(hint, index) in currentSubStepConfig.hints" :key="index">
                      {{ hint }}
                    </li>
                  </ul>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </div>
      </transition>
    </div>

    <!-- 子步骤导航按钮 -->
    <div class="sub-step-actions">
      <el-button 
        v-if="currentSubStep > 0"
        @click="prevSubStep"
        :icon="ArrowLeft"
      >
        上一步
      </el-button>
      <el-button 
        v-if="currentSubStep < totalSubSteps - 1"
        type="primary"
        @click="nextSubStep"
        :icon="ArrowRight"
        :disabled="!canProceedToNext"
      >
        下一步
      </el-button>
      <el-button 
        v-if="currentSubStep === totalSubSteps - 1"
        type="success"
        @click="completeStep"
        :icon="Check"
        :disabled="!isStepComplete"
      >
        完成此步骤
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { 
  Tools, 
  Guide, 
  Setting, 
  CircleCheck, 
  Check, 
  Plus,
  ArrowLeft, 
  ArrowRight 
} from '@element-plus/icons-vue'

interface Props {
  config: any
  data: any
}

interface Emits {
  (e: 'update', data: any): void
  (e: 'next'): void
  (e: 'prev'): void
  (e: 'complete'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const currentSubStep = ref(0)
const completedInstructions = ref<Record<number, boolean[]>>({})
const parameterValues = ref<Record<string, any>>({})
const validationResults = ref<any[]>([])

// 计算属性
const totalSubSteps = computed(() => props.config.subSteps?.length || 0)
const currentSubStepConfig = computed(() => props.config.subSteps?.[currentSubStep.value])

const canProceedToNext = computed(() => {
  const subStep = currentSubStepConfig.value
  if (!subStep) return true
  
  // 检查必需的指令是否完成
  if (subStep.instructions) {
    const requiredInstructions = subStep.instructions.filter(inst => inst.required)
    const completed = completedInstructions.value[currentSubStep.value] || []
    return requiredInstructions.every((_, index) => completed[index])
  }
  
  return true
})

const isStepComplete = computed(() => {
  // 检查所有子步骤是否完成
  return props.config.subSteps?.every((_, index) => {
    const instructions = props.config.subSteps[index].instructions || []
    const completed = completedInstructions.value[index] || []
    return instructions.every((inst, instIndex) => !inst.required || completed[instIndex])
  })
})

// 方法
const goToSubStep = (index: number) => {
  if (index <= currentSubStep.value || canProceedToNext.value) {
    currentSubStep.value = index
  }
}

const nextSubStep = () => {
  if (canProceedToNext.value && currentSubStep.value < totalSubSteps.value - 1) {
    currentSubStep.value++
  }
}

const prevSubStep = () => {
  if (currentSubStep.value > 0) {
    currentSubStep.value--
  }
}

const getSubStepStatus = (index: number) => {
  if (index < currentSubStep.value) return 'finish'
  if (index === currentSubStep.value) return 'process'
  return 'wait'
}

const isInstructionCompleted = (index: number) => {
  const completed = completedInstructions.value[currentSubStep.value] || []
  return completed[index] || false
}

const toggleInstructionComplete = (index: number) => {
  if (!completedInstructions.value[currentSubStep.value]) {
    completedInstructions.value[currentSubStep.value] = []
  }
  completedInstructions.value[currentSubStep.value][index] = 
    !completedInstructions.value[currentSubStep.value][index]
  
  emitUpdate()
}

const handleParameterChange = (name: string, value: any) => {
  parameterValues.value[name] = value
  emitUpdate()
  validateParameters()
}

const validateParameters = () => {
  // 实现参数验证逻辑
  validationResults.value = []
}

const completeStep = () => {
  if (isStepComplete.value) {
    emit('complete')
    emit('next')
  }
}

const emitUpdate = () => {
  emit('update', {
    currentSubStep: currentSubStep.value,
    completedInstructions: completedInstructions.value,
    parameterValues: parameterValues.value
  })
}

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData) {
    currentSubStep.value = newData.currentSubStep || 0
    completedInstructions.value = newData.completedInstructions || {}
    parameterValues.value = newData.parameterValues || {}
  }
}, { immediate: true })
</script>

<style scoped>
.oscilloscope-operation-step {
  padding: 20px;
}

.step-header {
  margin-bottom: 30px;
  text-align: center;
}

.step-header h2 {
  color: #409eff;
  margin-bottom: 10px;
}

.step-description {
  color: #666;
  font-size: 16px;
}

.sub-steps-nav {
  margin-bottom: 30px;
}

.sub-step-content {
  min-height: 400px;
}

.instruction-list {
  list-style: none;
  padding: 0;
}

.instruction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: all 0.3s;
}

.instruction-item.completed {
  background-color: #f0f9ff;
  border-color: #409eff;
}

.instruction-content {
  flex: 1;
}

.instruction-text {
  font-size: 14px;
  line-height: 1.5;
}

.parameters-section,
.validation-section,
.hints-section {
  margin-top: 20px;
}

.parameter-unit {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

.sub-step-actions {
  margin-top: 30px;
  text-align: center;
}

.sub-step-actions .el-button {
  margin: 0 10px;
}

.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  transform: translateX(30px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateX(-30px);
  opacity: 0;
}
</style>
