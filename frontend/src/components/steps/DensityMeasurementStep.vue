<template>
  <div class="density-measurement-step">
    <div class="step-header">
      <h2><el-icon><Scale /></el-icon> {{ config.title || '密度测量步骤' }}</h2>
      <p class="step-description">{{ config.description }}</p>
    </div>

    <!-- 测量类型选择 -->
    <div class="measurement-type-selector">
      <el-radio-group v-model="currentMeasurementType" @change="handleMeasurementTypeChange">
        <el-radio-button 
          v-for="type in config.measurementTypes" 
          :key="type.id"
          :label="type.id"
        >
          {{ type.name }}
        </el-radio-button>
      </el-radio-group>
    </div>

    <!-- 当前测量类型的内容 -->
    <div class="measurement-content">
      <transition name="fade" mode="out-in">
        <div :key="currentMeasurementType" class="measurement-section">
          <div v-if="currentTypeConfig" class="type-content">
            <h3>{{ currentTypeConfig.name }}</h3>
            <p class="type-description">{{ currentTypeConfig.description }}</p>

            <!-- 测量指导 -->
            <div v-if="currentTypeConfig.instructions" class="instructions-section">
              <h4><el-icon><Guide /></el-icon> 测量步骤</h4>
              <el-steps :active="currentInstructionStep" direction="vertical">
                <el-step 
                  v-for="(instruction, index) in currentTypeConfig.instructions"
                  :key="index"
                  :title="instruction.title"
                  :description="instruction.description"
                  :status="getInstructionStatus(index)"
                >
                  <template #icon>
                    <el-icon v-if="getInstructionStatus(index) === 'finish'">
                      <Check />
                    </el-icon>
                    <span v-else>{{ index + 1 }}</span>
                  </template>
                </el-step>
              </el-steps>
            </div>

            <!-- 数据输入表格 -->
            <div v-if="currentTypeConfig.dataTable" class="data-table-section">
              <h4><el-icon><DataBoard /></el-icon> 数据记录</h4>
              <el-table 
                :data="measurementData[currentMeasurementType] || []" 
                border
                style="width: 100%"
                @cell-dblclick="handleCellEdit"
              >
                <el-table-column
                  v-for="column in currentTypeConfig.dataTable.columns"
                  :key="column.prop"
                  :prop="column.prop"
                  :label="column.label"
                  :width="column.width"
                >
                  <template #default="{ row, column: col, $index }">
                    <el-input-number
                      v-if="editingCell.row === $index && editingCell.column === col.property"
                      v-model="row[col.property]"
                      :precision="getColumnPrecision(col.property)"
                      :min="getColumnMin(col.property)"
                      :max="getColumnMax(col.property)"
                      size="small"
                      @blur="handleCellBlur"
                      @keyup.enter="handleCellBlur"
                      ref="editInput"
                    />
                    <span v-else>
                      {{ formatCellValue(row[col.property], col.property) }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                  <template #default="{ $index }">
                    <el-button 
                      size="small" 
                      type="primary" 
                      @click="addMeasurement"
                      v-if="$index === (measurementData[currentMeasurementType] || []).length - 1"
                    >
                      添加
                    </el-button>
                    <el-button 
                      size="small" 
                      type="danger" 
                      @click="removeMeasurement($index)"
                      v-if="(measurementData[currentMeasurementType] || []).length > 1"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 统计结果 -->
            <div v-if="statisticsResults[currentMeasurementType]" class="statistics-section">
              <h4><el-icon><DataAnalysis /></el-icon> 统计结果</h4>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-statistic 
                    title="平均值" 
                    :value="statisticsResults[currentMeasurementType].average"
                    :precision="3"
                    :suffix="currentTypeConfig.unit"
                  />
                </el-col>
                <el-col :span="8">
                  <el-statistic 
                    title="标准偏差" 
                    :value="statisticsResults[currentMeasurementType].stdDev"
                    :precision="4"
                    :suffix="currentTypeConfig.unit"
                  />
                </el-col>
                <el-col :span="8">
                  <el-statistic 
                    title="相对误差" 
                    :value="statisticsResults[currentMeasurementType].relativeError"
                    :precision="2"
                    suffix="%"
                  />
                </el-col>
              </el-row>
            </div>

            <!-- 操作提示 -->
            <div v-if="currentTypeConfig.tips" class="tips-section">
              <el-alert
                v-for="(tip, index) in currentTypeConfig.tips"
                :key="index"
                :title="tip.title"
                :description="tip.content"
                :type="tip.type || 'info'"
                :closable="false"
                show-icon
                style="margin-bottom: 10px"
              />
            </div>
          </div>
        </div>
      </transition>
    </div>

    <!-- 步骤控制 -->
    <div class="step-actions">
      <el-button @click="prevStep" :icon="ArrowLeft">
        上一步
      </el-button>
      <el-button 
        type="primary" 
        @click="nextStep" 
        :icon="ArrowRight"
        :disabled="!canProceed"
      >
        下一步
      </el-button>
      <el-button 
        type="success" 
        @click="calculateResults"
        :icon="Calculator"
        :disabled="!hasEnoughData"
      >
        计算结果
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { 
  Scale, 
  Guide, 
  DataBoard, 
  DataAnalysis,
  Check,
  ArrowLeft, 
  ArrowRight,
  Calculator
} from '@element-plus/icons-vue'

interface Props {
  config: any
  data: any
}

interface Emits {
  (e: 'update', data: any): void
  (e: 'next'): void
  (e: 'prev'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const currentMeasurementType = ref('mass')
const currentInstructionStep = ref(0)
const measurementData = ref<Record<string, any[]>>({})
const statisticsResults = ref<Record<string, any>>({})
const editingCell = ref({ row: -1, column: '' })

// 计算属性
const currentTypeConfig = computed(() => {
  return props.config.measurementTypes?.find(
    type => type.id === currentMeasurementType.value
  )
})

const canProceed = computed(() => {
  // 检查当前测量类型是否有足够的数据
  const data = measurementData.value[currentMeasurementType.value] || []
  return data.length >= (currentTypeConfig.value?.minMeasurements || 1)
})

const hasEnoughData = computed(() => {
  // 检查所有必需的测量类型是否都有数据
  return props.config.measurementTypes?.every(type => {
    const data = measurementData.value[type.id] || []
    return data.length >= (type.minMeasurements || 1)
  })
})

// 方法
const handleMeasurementTypeChange = (type: string) => {
  currentMeasurementType.value = type
  currentInstructionStep.value = 0
  initializeMeasurementData(type)
}

const initializeMeasurementData = (type: string) => {
  if (!measurementData.value[type]) {
    measurementData.value[type] = [createEmptyRow()]
  }
}

const createEmptyRow = () => {
  const row: Record<string, any> = {}
  currentTypeConfig.value?.dataTable?.columns?.forEach(column => {
    row[column.prop] = null
  })
  return row
}

const addMeasurement = () => {
  if (!measurementData.value[currentMeasurementType.value]) {
    measurementData.value[currentMeasurementType.value] = []
  }
  measurementData.value[currentMeasurementType.value].push(createEmptyRow())
  emitUpdate()
}

const removeMeasurement = (index: number) => {
  measurementData.value[currentMeasurementType.value].splice(index, 1)
  emitUpdate()
}

const handleCellEdit = (row: any, column: any, cell: any, event: any) => {
  const rowIndex = measurementData.value[currentMeasurementType.value].indexOf(row)
  editingCell.value = { row: rowIndex, column: column.property }
  nextTick(() => {
    // Focus on the input
  })
}

const handleCellBlur = () => {
  editingCell.value = { row: -1, column: '' }
  calculateStatistics()
  emitUpdate()
}

const calculateStatistics = () => {
  const data = measurementData.value[currentMeasurementType.value] || []
  if (data.length === 0) return

  const validData = data.filter(row => 
    Object.values(row).some(value => value !== null && value !== undefined)
  )

  if (validData.length === 0) return

  // 计算每列的统计数据
  const stats: any = {}
  currentTypeConfig.value?.dataTable?.columns?.forEach(column => {
    const values = validData
      .map(row => row[column.prop])
      .filter(value => value !== null && value !== undefined && !isNaN(value))
    
    if (values.length > 0) {
      const sum = values.reduce((a, b) => a + b, 0)
      const average = sum / values.length
      const variance = values.reduce((a, b) => a + Math.pow(b - average, 2), 0) / values.length
      const stdDev = Math.sqrt(variance)
      const relativeError = (stdDev / average) * 100

      stats[column.prop] = {
        average,
        stdDev,
        relativeError
      }
    }
  })

  statisticsResults.value[currentMeasurementType.value] = stats
}

const getInstructionStatus = (index: number) => {
  if (index < currentInstructionStep.value) return 'finish'
  if (index === currentInstructionStep.value) return 'process'
  return 'wait'
}

const getColumnPrecision = (prop: string) => {
  const column = currentTypeConfig.value?.dataTable?.columns?.find(col => col.prop === prop)
  return column?.precision || 2
}

const getColumnMin = (prop: string) => {
  const column = currentTypeConfig.value?.dataTable?.columns?.find(col => col.prop === prop)
  return column?.min || 0
}

const getColumnMax = (prop: string) => {
  const column = currentTypeConfig.value?.dataTable?.columns?.find(col => col.prop === prop)
  return column?.max || 1000
}

const formatCellValue = (value: any, prop: string) => {
  if (value === null || value === undefined) return '-'
  const precision = getColumnPrecision(prop)
  return Number(value).toFixed(precision)
}

const calculateResults = () => {
  // 触发结果计算
  emit('update', {
    measurementData: measurementData.value,
    statisticsResults: statisticsResults.value,
    calculationRequested: true
  })
}

const nextStep = () => {
  emit('next')
}

const prevStep = () => {
  emit('prev')
}

const emitUpdate = () => {
  emit('update', {
    currentMeasurementType: currentMeasurementType.value,
    measurementData: measurementData.value,
    statisticsResults: statisticsResults.value
  })
}

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData) {
    currentMeasurementType.value = newData.currentMeasurementType || 'mass'
    measurementData.value = newData.measurementData || {}
    statisticsResults.value = newData.statisticsResults || {}
  }
}, { immediate: true })

// 初始化
watch(() => props.config, (newConfig) => {
  if (newConfig && newConfig.measurementTypes) {
    newConfig.measurementTypes.forEach(type => {
      initializeMeasurementData(type.id)
    })
  }
}, { immediate: true })
</script>

<style scoped>
.density-measurement-step {
  padding: 20px;
}

.step-header {
  margin-bottom: 30px;
  text-align: center;
}

.step-header h2 {
  color: #409eff;
  margin-bottom: 10px;
}

.measurement-type-selector {
  margin-bottom: 30px;
  text-align: center;
}

.measurement-content {
  min-height: 500px;
}

.instructions-section,
.data-table-section,
.statistics-section,
.tips-section {
  margin-bottom: 30px;
}

.step-actions {
  margin-top: 30px;
  text-align: center;
}

.step-actions .el-button {
  margin: 0 10px;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
