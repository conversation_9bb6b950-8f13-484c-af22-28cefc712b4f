<template>
  <div class="current-control-circuit-experiment">
    <div class="experiment-header">
      <h1>恒流电路实验</h1>
      <p class="experiment-description">
        通过测量不同接入比例下的电流值，研究恒流电路的特性和规律
      </p>
    </div>

    <!-- 步骤导航 -->
    <div class="steps-navigation">
      <el-steps :active="currentStep - 1" align-center>
        <el-step title="实验准备" description="准备实验器材" />
        <el-step title="电路连接" description="连接实验电路" />
        <el-step title="k=1测量准备" description="设置k=1参数" />
        <el-step title="k=1数据测量" description="测量k=1数据" />
        <el-step title="k=0.1测量准备" description="设置k=0.1参数" />
        <el-step title="k=0.1数据测量" description="测量k=0.1数据" />
        <el-step title="数据分析" description="分析实验数据" />
      </el-steps>
    </div>

    <!-- 步骤内容 -->
    <div class="step-content">
      <transition name="slide-fade" mode="out-in">
        <div :key="currentStep" class="step-container">
          
          <!-- 步骤1: 实验准备 -->
          <div v-if="currentStep === 1" class="step-section">
            <div class="step-header">
              <h2><el-icon><Tools /></el-icon> 实验准备</h2>
            </div>

            <!-- 子步骤导航 -->
            <div class="substeps-nav">
              <el-steps :active="currentSubstep - 1" direction="vertical" size="small">
                <el-step 
                  v-for="(substep, index) in step1Substeps" 
                  :key="index"
                  :title="substep.title"
                  :description="substep.description"
                  :status="getSubstepStatus(1, index + 1)"
                />
              </el-steps>
            </div>

            <!-- 当前子步骤内容 -->
            <div class="substep-content">
              <transition name="fade" mode="out-in">
                <div :key="currentSubstep" class="substep-detail">
                  
                  <!-- 子步骤1: 准备晶体管直流稳压电源 -->
                  <div v-if="currentSubstep === 1" class="substep-item">
                    <h3>准备晶体管直流稳压电源</h3>
                    <p>请确认晶体管直流稳压电源是否可用，并检查以下事项：</p>
                    <ul>
                      <li>输出接线柱是否稳固</li>
                      <li>调压旋钮和调流旋钮是否灵活可控</li>
                    </ul>
                    <el-alert 
                      title="提示" 
                      description="晶体管直流稳压电源将用于为恒流电路提供稳定的电压。"
                      type="info" 
                      :closable="false"
                      show-icon
                    />
                  </div>

                  <!-- 子步骤2: 准备电阻箱 -->
                  <div v-if="currentSubstep === 2" class="substep-item">
                    <h3>准备电阻箱</h3>
                    <p>检查电阻箱的状态：</p>
                    <ul>
                      <li>确认电阻箱各档位旋钮工作正常</li>
                      <li>检查接线端子是否牢固</li>
                      <li>将电阻箱初始设置为0Ω</li>
                    </ul>
                    <el-alert 
                      title="注意" 
                      description="电阻箱将用作可变电阻Rz，需要在实验过程中调节其阻值。"
                      type="warning" 
                      :closable="false"
                      show-icon
                    />
                  </div>

                  <!-- 子步骤3: 准备滑线变阻器 -->
                  <div v-if="currentSubstep === 3" class="substep-item">
                    <h3>准备滑线变阻器</h3>
                    <p>检查滑线变阻器：</p>
                    <ul>
                      <li>确认滑动端可以自由移动</li>
                      <li>检查接线端子A、B是否牢固</li>
                      <li>将滑动端置于中间位置</li>
                    </ul>
                    <el-alert 
                      title="说明" 
                      description="滑线变阻器用作R0，通过改变接入比例来研究电流变化规律。"
                      type="info" 
                      :closable="false"
                      show-icon
                    />
                  </div>

                  <!-- 子步骤4: 准备电流表 -->
                  <div v-if="currentSubstep === 4" class="substep-item">
                    <h3>准备电流表</h3>
                    <p>检查电流表设置：</p>
                    <ul>
                      <li>确认电流表指针归零</li>
                      <li>检查正负接线端子</li>
                      <li>将量程设置为较大档位（如100mA）</li>
                    </ul>
                    <el-alert 
                      title="安全提醒" 
                      description="实验开始前必须将电流表设置为较大量程，防止电流过大损坏仪表。"
                      type="error" 
                      :closable="false"
                      show-icon
                    />
                  </div>

                  <!-- 子步骤5: 准备钮子开关 -->
                  <div v-if="currentSubstep === 5" class="substep-item">
                    <h3>准备钮子开关</h3>
                    <p>检查钮子开关：</p>
                    <ul>
                      <li>确认开关处于断开状态</li>
                      <li>检查开关接触是否良好</li>
                      <li>确认开关标识清晰</li>
                    </ul>
                    <el-alert 
                      title="重要" 
                      description="在连接电路和调节参数时，钮子开关必须保持断开状态。"
                      type="warning" 
                      :closable="false"
                      show-icon
                    />
                  </div>

                  <!-- 子步骤6: 准备导线 -->
                  <div v-if="currentSubstep === 6" class="substep-item">
                    <h3>准备导线</h3>
                    <p>检查连接导线：</p>
                    <ul>
                      <li>确认导线无破损</li>
                      <li>检查导线接头是否牢固</li>
                      <li>准备足够数量的导线</li>
                    </ul>
                    <el-alert 
                      title="提示" 
                      description="建议使用不同颜色的导线来区分不同的连接，便于检查电路。"
                      type="success" 
                      :closable="false"
                      show-icon
                    />
                  </div>

                </div>
              </transition>
            </div>

            <!-- 子步骤导航按钮 -->
            <div class="substep-navigation">
              <el-button 
                v-if="currentSubstep > 1"
                @click="prevSubstep"
                :icon="ArrowLeft"
              >
                上一步
              </el-button>
              <el-button 
                v-if="currentSubstep < step1Substeps.length"
                type="primary"
                @click="nextSubstep"
                :icon="ArrowRight"
              >
                下一步
              </el-button>
              <el-button 
                v-if="currentSubstep === step1Substeps.length"
                type="success"
                @click="nextStep"
                :icon="Check"
              >
                完成准备，进入下一步
              </el-button>
            </div>
          </div>

          <!-- 步骤2: 电路连接 -->
          <div v-if="currentStep === 2" class="step-section">
            <div class="step-header">
              <h2><el-icon><Connection /></el-icon> 电路连接</h2>
            </div>

            <div class="circuit-diagram">
              <el-image 
                src="/images/current_control_circuit/figure1.png" 
                alt="恒流电路详细连接图"
                fit="contain"
                style="width: 100%; max-width: 600px;"
              />
              <p class="diagram-caption">恒流电路连接示意图</p>
            </div>

            <div class="connection-checklist">
              <h3>连接检查清单</h3>
              <el-checkbox-group v-model="connectionChecklist">
                <div class="checklist-item">
                  <el-checkbox label="power_off">电源处于关闭状态</el-checkbox>
                </div>
                <div class="checklist-item">
                  <el-checkbox label="ammeter_polarity">注意电流表正负极连接</el-checkbox>
                </div>
                <div class="checklist-item">
                  <el-checkbox label="switch_open">开关处于断开状态</el-checkbox>
                </div>
                <div class="checklist-item">
                  <el-checkbox label="rheostat_center">滑线变阻器接触点置中部</el-checkbox>
                </div>
                <div class="checklist-item">
                  <el-checkbox label="wire_layout">导线排布整齐，无交叉短路风险</el-checkbox>
                </div>
              </el-checkbox-group>
            </div>

            <div v-if="isConnectionComplete" class="success-message">
              <el-alert
                title="连接完成"
                description="电路连接已完成并检查无误！您可以继续下一步实验。"
                type="success"
                :closable="false"
                show-icon
              />
            </div>

            <div class="step-navigation">
              <el-button @click="prevStep" :icon="ArrowLeft">
                上一步
              </el-button>
              <el-button 
                type="primary" 
                @click="nextStep" 
                :icon="ArrowRight"
                :disabled="!isConnectionComplete"
              >
                下一步
              </el-button>
            </div>
          </div>

          <!-- 步骤3: k=1测量准备 -->
          <div v-if="currentStep === 3" class="step-section">
            <div class="step-header">
              <h2><el-icon><Setting /></el-icon> k=1 测量准备</h2>
            </div>

            <!-- 进度条 -->
            <div class="progress-container">
              <el-progress 
                :percentage="(currentSubstep / step3Substeps.length) * 100" 
                :stroke-width="8"
                status="success"
              />
            </div>

            <!-- 子步骤内容 -->
            <div class="substep-content">
              <transition name="fade" mode="out-in">
                <div :key="currentSubstep" class="substep-detail">
                  
                  <!-- 子步骤1: 设置k=1 -->
                  <div v-if="currentSubstep === 1" class="substep-item">
                    <h3>设置 k=1</h3>
                    <p>现在我们需要设置电阻箱(Rz)与滑线变阻器(R0)的阻值，使 k=1：</p>
                    
                    <div class="parameter-explanation">
                      <h4>实验参数说明：</h4>
                      <ul>
                        <li><strong>K = Rz / R0</strong>：其中Rz为电阻箱设置的阻值，R0为滑线变阻器的总阻值</li>
                        <li><strong>接入比例 = l/l0</strong>：其中l为滑动端到绕线一端的长度，l0为滑线变阻器的总长度</li>
                        <li>当滑动端与绕线的金属端接触时，接入比例为0</li>
                        <li>当滑动端与绕线的另一端接触时，接入比例为1</li>
                      </ul>
                    </div>

                    <div class="setting-steps">
                      <h4>设置步骤：</h4>
                      <ol>
                        <li>确定滑线变阻器(R0)的总阻值，例如100Ω</li>
                        <li>将电阻箱(Rz)的阻值调整为与滑线变阻器总阻值相等，即100Ω</li>
                        <li>这样确保 k = Rz/R0 = 1</li>
                      </ol>
                    </div>

                    <el-alert 
                      title="操作要求" 
                      description="请调整电阻箱(Rz)，使其阻值等于滑线变阻器(R0)的总阻值。"
                      type="warning" 
                      :closable="false"
                      show-icon
                    />
                  </div>

                  <!-- 子步骤2: 设置电源电压 -->
                  <div v-if="currentSubstep === 2" class="substep-item">
                    <h3>设置电源电压</h3>
                    <p>设置电源输出电压为5伏特：</p>
                    
                    <div class="setting-steps">
                      <h4>设置步骤：</h4>
                      <ol>
                        <li>确保钮子开关仍处于断开状态</li>
                        <li>打开电源</li>
                        <li>缓慢调节电源调压旋钮，直到输出电压达到5伏特</li>
                        <li>读取电源面板上的电压表，确保电压稳定在5伏特</li>
                      </ol>
                    </div>

                    <el-alert 
                      title="注意" 
                      description="调节电压时应缓慢操作，避免电压突变。"
                      type="warning" 
                      :closable="false"
                      show-icon
                    />
                  </div>

                  <!-- 子步骤3: 电流表量程确认 -->
                  <div v-if="currentSubstep === 3" class="substep-item">
                    <h3>电流表量程确认</h3>
                    <p>确认电流表量程设置在安全范围：</p>
                    
                    <div class="setting-steps">
                      <h4>确认步骤：</h4>
                      <ol>
                        <li>再次确认电流表设置在较大量程(例如100mA)</li>
                        <li>闭合电键，逐渐调小电流表的量程，观察电流表指针偏转至较大值</li>
                        <li>准备记录表格，用于记录不同接入比例下的电流值</li>
                        <li>确保有足够的空间记录11个不同接入比例下的测量值(0.0到1.0)</li>
                      </ol>
                    </div>

                    <el-alert 
                      title="安全提示" 
                      description="在打开电源前，必须确认电流表量程已设置为较大值，以防电流过大损坏电流表。"
                      type="error" 
                      :closable="false"
                      show-icon
                    />
                  </div>

                </div>
              </transition>
            </div>

            <!-- 子步骤导航 -->
            <div class="substep-navigation">
              <el-button 
                v-if="currentSubstep > 1"
                @click="prevSubstep"
                :icon="ArrowLeft"
              >
                上一步
              </el-button>
              <el-button 
                v-if="currentSubstep < step3Substeps.length"
                type="primary"
                @click="nextSubstep"
                :icon="ArrowRight"
              >
                下一步
              </el-button>
              <el-button 
                v-if="currentSubstep === step3Substeps.length"
                type="success"
                @click="nextStep"
                :icon="Check"
              >
                完成设置，进入测量
              </el-button>
            </div>
          </div>

        </div>
      </transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  Tools, 
  Connection, 
  Setting,
  ArrowLeft, 
  ArrowRight, 
  Check 
} from '@element-plus/icons-vue'

// 响应式数据
const currentStep = ref(1)
const currentSubstep = ref(1)
const connectionChecklist = ref<string[]>([])

// 步骤配置
const step1Substeps = [
  { title: '准备晶体管直流稳压电源', description: '检查电源设备' },
  { title: '准备电阻箱', description: '检查电阻箱状态' },
  { title: '准备滑线变阻器', description: '检查变阻器' },
  { title: '准备电流表', description: '设置电流表' },
  { title: '准备钮子开关', description: '检查开关状态' },
  { title: '准备导线', description: '检查连接导线' }
]

const step3Substeps = [
  { title: '设置 k=1', description: '调整电阻比例' },
  { title: '设置电源电压', description: '调节到5V' },
  { title: '电流表量程确认', description: '确认安全量程' }
]

// 计算属性
const isConnectionComplete = computed(() => {
  const requiredChecks = ['power_off', 'ammeter_polarity', 'switch_open', 'rheostat_center', 'wire_layout']
  return requiredChecks.every(check => connectionChecklist.value.includes(check))
})

// 方法
const getSubstepStatus = (step: number, substep: number) => {
  if (currentStep.value > step) return 'finish'
  if (currentStep.value === step && currentSubstep.value > substep) return 'finish'
  if (currentStep.value === step && currentSubstep.value === substep) return 'process'
  return 'wait'
}

const nextStep = () => {
  if (currentStep.value < 7) {
    currentStep.value++
    currentSubstep.value = 1
  }
}

const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
    currentSubstep.value = 1
  }
}

const nextSubstep = () => {
  const maxSubsteps = currentStep.value === 1 ? step1Substeps.length : 
                     currentStep.value === 3 ? step3Substeps.length : 1
  if (currentSubstep.value < maxSubsteps) {
    currentSubstep.value++
  }
}

const prevSubstep = () => {
  if (currentSubstep.value > 1) {
    currentSubstep.value--
  }
}
</script>

<style scoped>
.current-control-circuit-experiment {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.experiment-header {
  text-align: center;
  margin-bottom: 30px;
}

.experiment-header h1 {
  color: #409eff;
  margin-bottom: 10px;
}

.experiment-description {
  color: #666;
  font-size: 16px;
}

.steps-navigation {
  margin-bottom: 40px;
}

.step-content {
  min-height: 600px;
}

.step-section {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.step-header h2 {
  color: #409eff;
  margin-bottom: 20px;
}

.substeps-nav {
  margin: 20px 0;
}

.substep-content {
  margin: 30px 0;
}

.substep-item h3 {
  color: #333;
  margin-bottom: 15px;
}

.substep-item p {
  margin-bottom: 15px;
  line-height: 1.6;
}

.substep-item ul, .substep-item ol {
  margin-bottom: 20px;
  padding-left: 20px;
}

.substep-item li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.parameter-explanation, .setting-steps {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin: 15px 0;
}

.parameter-explanation h4, .setting-steps h4 {
  color: #409eff;
  margin-bottom: 10px;
}

.circuit-diagram {
  text-align: center;
  margin: 30px 0;
}

.diagram-caption {
  margin-top: 10px;
  color: #666;
  font-style: italic;
}

.connection-checklist {
  margin: 30px 0;
}

.checklist-item {
  margin-bottom: 10px;
}

.progress-container {
  margin: 20px 0;
}

.substep-navigation, .step-navigation {
  margin-top: 30px;
  text-align: center;
}

.substep-navigation .el-button, .step-navigation .el-button {
  margin: 0 10px;
}

.success-message {
  margin: 20px 0;
}

.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  transform: translateX(30px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateX(-30px);
  opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
