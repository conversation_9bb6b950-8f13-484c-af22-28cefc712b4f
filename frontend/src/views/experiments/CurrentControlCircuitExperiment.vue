<template>
  <div class="current-control-circuit-experiment">
    <div class="experiment-header">
      <h1>恒流电路实验</h1>
      <p class="experiment-description">
        通过测量不同接入比例下的电流值，研究恒流电路的特性和规律
      </p>
    </div>

    <!-- 步骤导航 -->
    <div class="steps-navigation">
      <el-steps :active="currentStep - 1" align-center>
        <el-step title="实验准备" description="准备实验器材" />
        <el-step title="电路连接" description="连接实验电路" />
        <el-step title="k=1测量准备" description="设置k=1参数" />
        <el-step title="k=1数据测量" description="测量k=1数据" />
        <el-step title="k=0.1测量准备" description="设置k=0.1参数" />
        <el-step title="k=0.1数据测量" description="测量k=0.1数据" />
        <el-step title="数据分析" description="分析实验数据" />
      </el-steps>
    </div>

    <!-- 步骤内容 -->
    <div class="step-content">
      <transition name="slide-fade" mode="out-in">
        <div :key="currentStep" class="step-container">
          
          <!-- 步骤1: 实验准备 -->
          <div v-if="currentStep === 1" class="step-section">
            <div class="step-header">
              <h2><el-icon><Tools /></el-icon> 实验准备</h2>
            </div>

            <!-- 子步骤导航 -->
            <div class="substeps-nav">
              <el-steps :active="currentSubstep - 1" direction="vertical" size="small">
                <el-step 
                  v-for="(substep, index) in step1Substeps" 
                  :key="index"
                  :title="substep.title"
                  :description="substep.description"
                  :status="getSubstepStatus(1, index + 1)"
                />
              </el-steps>
            </div>

            <!-- 当前子步骤内容 -->
            <div class="substep-content">
              <transition name="fade" mode="out-in">
                <div :key="currentSubstep" class="substep-detail">
                  
                  <!-- 子步骤1: 准备晶体管直流稳压电源 -->
                  <div v-if="currentSubstep === 1" class="substep-item">
                    <h3>准备晶体管直流稳压电源</h3>
                    <div class="detailed-instructions">
                      <h4>操作步骤：</h4>
                      <ol>
                        <li><strong>检查电源状态：</strong>确认电源处于关闭状态，电源指示灯熄灭</li>
                        <li><strong>检查接线端子：</strong>检查正负极输出接线柱是否牢固，无松动</li>
                        <li><strong>检查调节旋钮：</strong>
                          <ul>
                            <li>调压旋钮：逆时针转到底（输出电压为0）</li>
                            <li>调流旋钮：顺时针转到底（限流最大）</li>
                          </ul>
                        </li>
                        <li><strong>检查显示表：</strong>确认电压表和电流表指针归零</li>
                      </ol>
                    </div>
                    <el-alert
                      title="安全提醒"
                      description="在连接电路前，必须确保电源处于关闭状态，调压旋钮调至最小。"
                      type="warning"
                      :closable="false"
                      show-icon
                    />
                  </div>

                  <!-- 子步骤2: 准备电阻箱 -->
                  <div v-if="currentSubstep === 2" class="substep-item">
                    <h3>准备电阻箱</h3>
                    <div class="detailed-instructions">
                      <h4>操作步骤：</h4>
                      <ol>
                        <li><strong>检查电阻箱外观：</strong>确认电阻箱无破损，各档位标识清晰</li>
                        <li><strong>检查接线端子：</strong>确认A、B两个接线端子牢固，无松动</li>
                        <li><strong>初始化设置：</strong>
                          <ul>
                            <li>将所有档位旋钮都调到"0"位置</li>
                            <li>此时电阻箱阻值为0Ω</li>
                            <li>记录电阻箱的最大阻值（通常为9999Ω或99999Ω）</li>
                          </ul>
                        </li>
                        <li><strong>功能测试：</strong>轻轻转动各档位旋钮，确认转动顺畅，定位准确</li>
                      </ol>
                    </div>
                    <el-alert
                      title="重要说明"
                      description="电阻箱作为Rz使用，实验中需要设置为100Ω（k=1时）和10Ω（k=0.1时）。"
                      type="info"
                      :closable="false"
                      show-icon
                    />
                  </div>

                  <!-- 子步骤3: 准备滑线变阻器 -->
                  <div v-if="currentSubstep === 3" class="substep-item">
                    <h3>准备滑线变阻器</h3>
                    <p>检查滑线变阻器：</p>
                    <ul>
                      <li>确认滑动端可以自由移动</li>
                      <li>检查接线端子A、B是否牢固</li>
                      <li>将滑动端置于中间位置</li>
                    </ul>
                    <el-alert 
                      title="说明" 
                      description="滑线变阻器用作R0，通过改变接入比例来研究电流变化规律。"
                      type="info" 
                      :closable="false"
                      show-icon
                    />
                  </div>

                  <!-- 子步骤4: 准备电流表 -->
                  <div v-if="currentSubstep === 4" class="substep-item">
                    <h3>准备电流表</h3>
                    <p>检查电流表设置：</p>
                    <ul>
                      <li>确认电流表指针归零</li>
                      <li>检查正负接线端子</li>
                      <li>将量程设置为较大档位（如100mA）</li>
                    </ul>
                    <el-alert 
                      title="安全提醒" 
                      description="实验开始前必须将电流表设置为较大量程，防止电流过大损坏仪表。"
                      type="error" 
                      :closable="false"
                      show-icon
                    />
                  </div>

                  <!-- 子步骤5: 准备钮子开关 -->
                  <div v-if="currentSubstep === 5" class="substep-item">
                    <h3>准备钮子开关</h3>
                    <p>检查钮子开关：</p>
                    <ul>
                      <li>确认开关处于断开状态</li>
                      <li>检查开关接触是否良好</li>
                      <li>确认开关标识清晰</li>
                    </ul>
                    <el-alert 
                      title="重要" 
                      description="在连接电路和调节参数时，钮子开关必须保持断开状态。"
                      type="warning" 
                      :closable="false"
                      show-icon
                    />
                  </div>

                  <!-- 子步骤6: 准备导线 -->
                  <div v-if="currentSubstep === 6" class="substep-item">
                    <h3>准备导线</h3>
                    <p>检查连接导线：</p>
                    <ul>
                      <li>确认导线无破损</li>
                      <li>检查导线接头是否牢固</li>
                      <li>准备足够数量的导线</li>
                    </ul>
                    <el-alert 
                      title="提示" 
                      description="建议使用不同颜色的导线来区分不同的连接，便于检查电路。"
                      type="success" 
                      :closable="false"
                      show-icon
                    />
                  </div>

                </div>
              </transition>
            </div>

            <!-- 子步骤导航按钮 -->
            <div class="substep-navigation">
              <el-button 
                v-if="currentSubstep > 1"
                @click="prevSubstep"
                :icon="ArrowLeft"
              >
                上一步
              </el-button>
              <el-button 
                v-if="currentSubstep < step1Substeps.length"
                type="primary"
                @click="nextSubstep"
                :icon="ArrowRight"
              >
                下一步
              </el-button>
              <el-button 
                v-if="currentSubstep === step1Substeps.length"
                type="success"
                @click="nextStep"
                :icon="Check"
              >
                完成准备，进入下一步
              </el-button>
            </div>
          </div>

          <!-- 步骤2: 电路连接 -->
          <div v-if="currentStep === 2" class="step-section">
            <div class="step-header">
              <h2><el-icon><Connection /></el-icon> 电路连接</h2>
            </div>

            <div class="circuit-diagram">
              <el-image
                src="/images/current_control_circuit/figure1.png"
                alt="恒流电路详细连接图"
                fit="contain"
                style="width: 100%; max-width: 600px;"
              />
              <p class="diagram-caption">恒流电路连接示意图</p>
            </div>

            <div class="connection-checklist">
              <h3>连接检查清单</h3>
              <el-checkbox-group v-model="connectionChecklist">
                <div class="checklist-item">
                  <el-checkbox label="power_off">电源处于关闭状态</el-checkbox>
                </div>
                <div class="checklist-item">
                  <el-checkbox label="ammeter_polarity">注意电流表正负极连接</el-checkbox>
                </div>
                <div class="checklist-item">
                  <el-checkbox label="switch_open">开关处于断开状态</el-checkbox>
                </div>
                <div class="checklist-item">
                  <el-checkbox label="rheostat_center">滑线变阻器接触点置中部</el-checkbox>
                </div>
                <div class="checklist-item">
                  <el-checkbox label="wire_layout">导线排布整齐，无交叉短路风险</el-checkbox>
                </div>
              </el-checkbox-group>
            </div>

            <div v-if="isConnectionComplete" class="success-message">
              <el-alert
                title="连接完成"
                description="电路连接已完成并检查无误！您可以继续下一步实验。"
                type="success"
                :closable="false"
                show-icon
              />
            </div>

            <div class="step-navigation">
              <el-button @click="prevStep" :icon="ArrowLeft">
                上一步
              </el-button>
              <el-button
                type="primary"
                @click="nextStep"
                :icon="ArrowRight"
                :disabled="!isConnectionComplete"
              >
                下一步
              </el-button>
            </div>
          </div>

          <!-- 步骤3: k=1测量准备 -->
          <div v-if="currentStep === 3" class="step-section">
            <div class="step-header">
              <h2><el-icon><Setting /></el-icon> k=1 测量准备</h2>
            </div>

            <!-- 进度条 -->
            <div class="progress-container">
              <el-progress
                :percentage="(currentSubstep / step3Substeps.length) * 100"
                :stroke-width="8"
                status="success"
              />
            </div>

            <!-- 子步骤内容 -->
            <div class="substep-content">
              <transition name="fade" mode="out-in">
                <div :key="currentSubstep" class="substep-detail">

                  <!-- 子步骤1: 设置k=1 -->
                  <div v-if="currentSubstep === 1" class="substep-item">
                    <h3>设置 k=1</h3>
                    <p>现在我们需要设置电阻箱(Rz)与滑线变阻器(R0)的阻值，使 k=1：</p>

                    <div class="parameter-explanation">
                      <h4>实验参数说明：</h4>
                      <ul>
                        <li><strong>K = Rz / R0</strong>：其中Rz为电阻箱设置的阻值，R0为滑线变阻器的总阻值</li>
                        <li><strong>接入比例 = l/l0</strong>：其中l为滑动端到绕线一端的长度，l0为滑线变阻器的总长度</li>
                        <li>当滑动端与绕线的金属端接触时，接入比例为0</li>
                        <li>当滑动端与绕线的另一端接触时，接入比例为1</li>
                      </ul>
                    </div>

                    <div class="setting-steps">
                      <h4>设置步骤：</h4>
                      <ol>
                        <li>确定滑线变阻器(R0)的总阻值，例如100Ω</li>
                        <li>将电阻箱(Rz)的阻值调整为与滑线变阻器总阻值相等，即100Ω</li>
                        <li>这样确保 k = Rz/R0 = 1</li>
                      </ol>
                    </div>

                    <el-alert
                      title="操作要求"
                      description="请调整电阻箱(Rz)，使其阻值等于滑线变阻器(R0)的总阻值。"
                      type="warning"
                      :closable="false"
                      show-icon
                    />
                  </div>

                  <!-- 子步骤2: 设置电源电压 -->
                  <div v-if="currentSubstep === 2" class="substep-item">
                    <h3>设置电源电压</h3>
                    <p>设置电源输出电压为5伏特：</p>

                    <div class="setting-steps">
                      <h4>设置步骤：</h4>
                      <ol>
                        <li>确保钮子开关仍处于断开状态</li>
                        <li>打开电源</li>
                        <li>缓慢调节电源调压旋钮，直到输出电压达到5伏特</li>
                        <li>读取电源面板上的电压表，确保电压稳定在5伏特</li>
                      </ol>
                    </div>

                    <el-alert
                      title="注意"
                      description="调节电压时应缓慢操作，避免电压突变。"
                      type="warning"
                      :closable="false"
                      show-icon
                    />
                  </div>

                  <!-- 子步骤3: 电流表量程确认 -->
                  <div v-if="currentSubstep === 3" class="substep-item">
                    <h3>电流表量程确认</h3>
                    <p>确认电流表量程设置在安全范围：</p>

                    <div class="setting-steps">
                      <h4>确认步骤：</h4>
                      <ol>
                        <li>再次确认电流表设置在较大量程(例如100mA)</li>
                        <li>闭合电键，逐渐调小电流表的量程，观察电流表指针偏转至较大值</li>
                        <li>准备记录表格，用于记录不同接入比例下的电流值</li>
                        <li>确保有足够的空间记录11个不同接入比例下的测量值(0.0到1.0)</li>
                      </ol>
                    </div>

                    <el-alert
                      title="安全提示"
                      description="在打开电源前，必须确认电流表量程已设置为较大值，以防电流过大损坏电流表。"
                      type="error"
                      :closable="false"
                      show-icon
                    />
                  </div>

                </div>
              </transition>
            </div>

            <!-- 子步骤导航 -->
            <div class="substep-navigation">
              <el-button
                v-if="currentSubstep > 1"
                @click="prevSubstep"
                :icon="ArrowLeft"
              >
                上一步
              </el-button>
              <el-button
                v-if="currentSubstep < step3Substeps.length"
                type="primary"
                @click="nextSubstep"
                :icon="ArrowRight"
              >
                下一步
              </el-button>
              <el-button
                v-if="currentSubstep === step3Substeps.length"
                type="success"
                @click="nextStep"
                :icon="Check"
              >
                完成设置，进入测量
              </el-button>
            </div>
          </div>

          <!-- 步骤4: k=1数据测量 -->
          <div v-if="currentStep === 4" class="step-section">
            <div class="step-header">
              <h2><el-icon><DataBoard /></el-icon> k=1 数据测量</h2>
            </div>

            <p>现在开始测量 k=1 时不同接入比例下的电流值：</p>

            <div class="measurement-instructions">
              <h3>测量步骤：</h3>
              <ol>
                <li>保持钮子开关闭合</li>
                <li>调整滑线变阻器从接入比例。从0.0开始，逐步调整滑线变阻器的滑动端位置</li>
                <li>每调整一次位置，记录对应的电流值</li>
                <li>完成所有测量后，断开钮子开关</li>
              </ol>
            </div>

            <el-alert
              title="重要提醒"
              description="接入比例一定从0.0开始测量"
              type="warning"
              :closable="false"
              show-icon
              style="margin-bottom: 20px;"
            />

            <div class="data-table-container">
              <h3>k=1 数据记录表</h3>
              <el-table :data="k1Data" border style="width: 100%">
                <el-table-column prop="index" label="序号" width="80" align="center" />
                <el-table-column prop="ratio" label="接入比例" width="120" align="center" />
                <el-table-column label="k=1时的电流 (mA)" width="200" align="center">
                  <template #default="{ row, $index }">
                    <el-input-number
                      v-model="row.current"
                      :precision="2"
                      :step="0.01"
                      :min="0"
                      :max="1000"
                      size="small"
                      placeholder="输入电流值"
                      @change="validateK1Data"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div v-if="k1DataComplete" class="success-message">
              <el-alert
                title="数据记录完成"
                description="k=1 的所有数据已记录完成！可以进入下一步。"
                type="success"
                :closable="false"
                show-icon
              />
            </div>

            <div class="step-navigation">
              <el-button @click="prevStep" :icon="ArrowLeft">
                上一步
              </el-button>
              <el-button
                type="primary"
                @click="nextStep"
                :icon="ArrowRight"
                :disabled="!k1DataComplete"
              >
                下一步
              </el-button>
            </div>
          </div>

          <!-- 步骤5: k=0.1测量准备 -->
          <div v-if="currentStep === 5" class="step-section">
            <div class="step-header">
              <h2><el-icon><Setting /></el-icon> k=0.1 测量准备</h2>
            </div>

            <!-- 进度条 -->
            <div class="progress-container">
              <el-progress
                :percentage="(currentSubstep / step5Substeps.length) * 100"
                :stroke-width="8"
                status="success"
              />
            </div>

            <!-- 子步骤内容 -->
            <div class="substep-content">
              <transition name="fade" mode="out-in">
                <div :key="currentSubstep" class="substep-detail">

                  <!-- 子步骤1: 断开电路 -->
                  <div v-if="currentSubstep === 1" class="substep-item">
                    <h3>断开电路</h3>
                    <p>在进行新的测量前，需要断开电路：</p>
                    <div class="setting-steps">
                      <h4>操作步骤：</h4>
                      <ol>
                        <li>断开钮子开关</li>
                        <li>确认电路中无电流流过</li>
                      </ol>
                    </div>
                  </div>

                  <!-- 子步骤2: 调节电源电压为零 -->
                  <div v-if="currentSubstep === 2" class="substep-item">
                    <h3>调节电源电压为零</h3>
                    <p>将电源输出电压调节为零：</p>
                    <div class="setting-steps">
                      <h4>操作步骤：</h4>
                      <ol>
                        <li>逆时针旋转电源调压旋钮</li>
                        <li>直到电源面板上的电压表显示为零</li>
                      </ol>
                    </div>
                  </div>

                  <!-- 子步骤3: 设置k=0.1 -->
                  <div v-if="currentSubstep === 3" class="substep-item">
                    <h3>设置 k=0.1</h3>
                    <div class="detailed-instructions">
                      <h4>详细操作步骤：</h4>
                      <ol>
                        <li><strong>确认滑线变阻器总阻值：</strong>
                          <ul>
                            <li>查看滑线变阻器标识，确认总阻值R0（通常为100Ω）</li>
                            <li>如果标识不清，可用万用表测量A、B两端的总阻值</li>
                          </ul>
                        </li>
                        <li><strong>计算所需电阻值：</strong>
                          <ul>
                            <li>根据公式：Rz = k × R0 = 0.1 × R0</li>
                            <li>如果R0 = 100Ω，则Rz = 0.1 × 100Ω = 10Ω</li>
                          </ul>
                        </li>
                        <li><strong>调节电阻箱：</strong>
                          <ul>
                            <li>将电阻箱的十位档调到"1"</li>
                            <li>个位档调到"0"</li>
                            <li>其他档位保持"0"</li>
                            <li>此时电阻箱显示10Ω</li>
                          </ul>
                        </li>
                        <li><strong>验证设置：</strong>用万用表测量电阻箱A、B端，确认阻值为10Ω</li>
                      </ol>
                    </div>
                    <el-alert
                      title="关键提醒"
                      description="k=0.1时电阻值较小，电流变化会更敏感，这是恒流电路特性的重要体现。"
                      type="warning"
                      :closable="false"
                      show-icon
                    />
                  </div>

                  <!-- 子步骤4: 调整滑线变阻器初始位置 -->
                  <div v-if="currentSubstep === 4" class="substep-item">
                    <h3>调整滑线变阻器初始位置</h3>
                    <p>将滑线变阻器滑动端置于接入比例为零的位置：</p>
                    <div class="setting-steps">
                      <h4>操作步骤：</h4>
                      <ol>
                        <li>移动滑线变阻器的滑动端</li>
                        <li>将滑动端与绕线的金属端接触</li>
                        <li>此时接入比例为零</li>
                      </ol>
                    </div>
                  </div>

                  <!-- 子步骤5: 闭合钮子开关 -->
                  <div v-if="currentSubstep === 5" class="substep-item">
                    <h3>闭合钮子开关</h3>
                    <p>准备开始测量，闭合钮子开关：</p>
                    <div class="setting-steps">
                      <h4>操作步骤：</h4>
                      <ol>
                        <li>确认电源电压仍为零</li>
                        <li>闭合钮子开关</li>
                        <li>电路此时已连通，但因为电压为零，所以无电流流过</li>
                      </ol>
                    </div>
                  </div>

                  <!-- 子步骤6: 调节电源电压 -->
                  <div v-if="currentSubstep === 6" class="substep-item">
                    <h3>调节电源电压</h3>
                    <div class="detailed-instructions">
                      <h4>详细操作步骤：</h4>
                      <ol>
                        <li><strong>确认目标电流值：</strong>
                          <ul>
                            <li>查看k=1时接入比例为0.0的电流值（第一个测量值）</li>
                            <li>记录这个数值作为目标电流</li>
                          </ul>
                        </li>
                        <li><strong>缓慢调节电压：</strong>
                          <ul>
                            <li>非常缓慢地顺时针旋转调压旋钮（每次转动不超过1/8圈）</li>
                            <li>观察电流表指针变化</li>
                            <li>k=0.1时电流变化很敏感，需要精细调节</li>
                          </ul>
                        </li>
                        <li><strong>精确调节：</strong>
                          <ul>
                            <li>当电流接近目标值时，进行微调</li>
                            <li>使电流值尽可能接近k=1时的初始电流值</li>
                            <li>允许±0.1mA的误差范围</li>
                          </ul>
                        </li>
                        <li><strong>稳定观察：</strong>等待10-15秒，确认电流读数稳定</li>
                      </ol>
                    </div>
                    <el-alert
                      title="重要提醒"
                      description="k=0.1时电路对电压变化非常敏感，调节时必须非常缓慢和精细。"
                      type="error"
                      :closable="false"
                      show-icon
                    />
                  </div>

                  <!-- 子步骤7: 记录初始电流值 -->
                  <div v-if="currentSubstep === 7" class="substep-item">
                    <h3>记录初始电流值</h3>
                    <p>记录 k=0.1 时接入比例为 0.0 的电流值：</p>
                    <el-alert
                      title="准备完成"
                      description="k=0.1 测量准备工作已完成！您可以继续下一步实验。"
                      type="success"
                      :closable="false"
                      show-icon
                    />
                  </div>

                </div>
              </transition>
            </div>

            <!-- 子步骤导航 -->
            <div class="substep-navigation">
              <el-button
                v-if="currentSubstep > 1"
                @click="prevSubstep"
                :icon="ArrowLeft"
              >
                上一步
              </el-button>
              <el-button
                v-if="currentSubstep < step5Substeps.length"
                type="primary"
                @click="nextSubstep"
                :icon="ArrowRight"
              >
                下一步
              </el-button>
              <el-button
                v-if="currentSubstep === step5Substeps.length"
                type="success"
                @click="nextStep"
                :icon="Check"
              >
                完成设置，进入测量
              </el-button>
            </div>
          </div>

          <!-- 步骤6: k=0.1数据测量 -->
          <div v-if="currentStep === 6" class="step-section">
            <div class="step-header">
              <h2><el-icon><DataBoard /></el-icon> k=0.1 数据测量</h2>
            </div>

            <p>现在开始测量 k=0.1 时不同接入比例下的电流值：</p>

            <div class="measurement-instructions">
              <h3>测量步骤：</h3>
              <ol>
                <li>保持钮子开关闭合状态</li>
                <li>从接入比例0.0开始，逐步调整滑线变阻器的滑动端位置</li>
                <li>每调整一次位置，记录对应的电流值</li>
                <li>注意：k=0.1时电流变化会比k=1时更明显</li>
                <li>完成所有测量后，断开钮子开关</li>
              </ol>
            </div>

            <el-alert
              title="重要提醒"
              description="k=0.1时电流变化更敏感，请仔细观察电流表读数"
              type="warning"
              :closable="false"
              show-icon
              style="margin-bottom: 20px;"
            />

            <div class="data-table-container">
              <h3>k=0.1 数据记录表</h3>
              <el-table :data="k01Data" border style="width: 100%">
                <el-table-column prop="index" label="序号" width="80" align="center" />
                <el-table-column prop="ratio" label="接入比例" width="120" align="center" />
                <el-table-column label="k=0.1时的电流 (mA)" width="200" align="center">
                  <template #default="{ row }">
                    <el-input-number
                      v-model="row.current"
                      :precision="2"
                      :step="0.01"
                      :min="0"
                      :max="1000"
                      size="small"
                      placeholder="输入电流值"
                      @change="validateK01Data"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div v-if="k01DataComplete" class="success-message">
              <el-alert
                title="数据记录完成"
                description="k=0.1 的所有数据已记录完成！可以进入数据分析步骤。"
                type="success"
                :closable="false"
                show-icon
              />
            </div>

            <div class="step-navigation">
              <el-button @click="prevStep" :icon="ArrowLeft">
                上一步
              </el-button>
              <el-button
                type="primary"
                @click="nextStep"
                :icon="ArrowRight"
                :disabled="!k01DataComplete"
              >
                下一步
              </el-button>
            </div>
          </div>

          <!-- 步骤7: 数据分析与图形生成 -->
          <div v-if="currentStep === 7" class="step-section">
            <div class="step-header">
              <h2><el-icon><TrendCharts /></el-icon> 数据分析与图形生成</h2>
            </div>

            <p>根据测量的数据，我们可以绘制电流与接入比例的关系图：</p>

            <div class="analysis-actions">
              <el-button
                type="primary"
                @click="generatePlot"
                :loading="isGeneratingPlot"
                :icon="TrendCharts"
              >
                生成图形
              </el-button>
              <el-button
                type="success"
                @click="analyzeData"
                :loading="isAnalyzing"
                :icon="DataAnalysis"
                style="margin-left: 10px;"
              >
                AI 分析数据
              </el-button>
            </div>

            <!-- 数据汇总表 -->
            <div v-if="showDataSummary" class="data-summary-container">
              <h3>实验数据汇总</h3>
              <el-table :data="summaryData" border style="width: 100%">
                <el-table-column prop="ratio" label="接入比例" width="120" align="center" />
                <el-table-column prop="k1Current" label="k=1时的电流(mA)" width="150" align="center" />
                <el-table-column prop="k01Current" label="k=0.1时的电流(mA)" width="150" align="center" />
                <el-table-column prop="difference" label="电流差值(mA)" width="150" align="center" />
              </el-table>
            </div>

            <!-- 图形显示区域 -->
            <div v-if="plotImageUrl" class="plot-container">
              <h3>电流-接入比例关系图</h3>
              <el-image
                :src="plotImageUrl"
                alt="实验数据图表"
                fit="contain"
                style="width: 100%; max-width: 800px;"
              />
            </div>

            <!-- AI分析结果 -->
            <div v-if="analysisResult" class="analysis-container">
              <h3>AI 分析结果</h3>
              <div class="analysis-content" v-html="analysisResult"></div>
            </div>

            <div class="step-navigation">
              <el-button @click="prevStep" :icon="ArrowLeft">
                上一步
              </el-button>
              <el-button
                type="success"
                @click="finishExperiment"
                :icon="Check"
              >
                完成实验
              </el-button>
            </div>
          </div>

        </div>
      </transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Tools,
  Connection,
  Setting,
  DataBoard,
  TrendCharts,
  DataAnalysis,
  ArrowLeft,
  ArrowRight,
  Check
} from '@element-plus/icons-vue'

// 响应式数据
const currentStep = ref(1)
const currentSubstep = ref(1)
const connectionChecklist = ref<string[]>([])

// 实验数据类型定义
interface ExperimentData {
  index: number
  ratio: number
  current: number | null
}

// 实验数据
const k1Data = ref<ExperimentData[]>([
  { index: 1, ratio: 0.0, current: null },
  { index: 2, ratio: 0.1, current: null },
  { index: 3, ratio: 0.2, current: null },
  { index: 4, ratio: 0.3, current: null },
  { index: 5, ratio: 0.4, current: null },
  { index: 6, ratio: 0.5, current: null },
  { index: 7, ratio: 0.6, current: null },
  { index: 8, ratio: 0.7, current: null },
  { index: 9, ratio: 0.8, current: null },
  { index: 10, ratio: 0.9, current: null },
  { index: 11, ratio: 1.0, current: null }
])

const k01Data = ref<ExperimentData[]>([
  { index: 1, ratio: 0.0, current: null },
  { index: 2, ratio: 0.1, current: null },
  { index: 3, ratio: 0.2, current: null },
  { index: 4, ratio: 0.3, current: null },
  { index: 5, ratio: 0.4, current: null },
  { index: 6, ratio: 0.5, current: null },
  { index: 7, ratio: 0.6, current: null },
  { index: 8, ratio: 0.7, current: null },
  { index: 9, ratio: 0.8, current: null },
  { index: 10, ratio: 0.9, current: null },
  { index: 11, ratio: 1.0, current: null }
])

// 步骤配置
const step1Substeps = [
  { title: '准备晶体管直流稳压电源', description: '检查电源设备' },
  { title: '准备电阻箱', description: '检查电阻箱状态' },
  { title: '准备滑线变阻器', description: '检查变阻器' },
  { title: '准备电流表', description: '设置电流表' },
  { title: '准备钮子开关', description: '检查开关状态' },
  { title: '准备导线', description: '检查连接导线' }
]

const step3Substeps = [
  { title: '设置 k=1', description: '调整电阻比例' },
  { title: '设置电源电压', description: '调节到5V' },
  { title: '电流表量程确认', description: '确认安全量程' }
]

const step5Substeps = [
  { title: '断开电路', description: '断开钮子开关' },
  { title: '调节电源电压为零', description: '将电压调为0V' },
  { title: '设置 k=0.1', description: '调整电阻比例' },
  { title: '调整滑线变阻器初始位置', description: '设置接入比例为0' },
  { title: '闭合钮子开关', description: '连通电路' },
  { title: '调节电源电压', description: '调节到合适电压' },
  { title: '记录初始电流值', description: '记录起始数据' }
]

// 计算属性
const isConnectionComplete = computed(() => {
  const requiredChecks = ['power_off', 'ammeter_polarity', 'switch_open', 'rheostat_center', 'wire_layout']
  return requiredChecks.every(check => connectionChecklist.value.includes(check))
})

const k1DataComplete = computed(() => {
  return k1Data.value.every(item => item.current !== null && item.current > 0)
})

const k01DataComplete = computed(() => {
  return k01Data.value.every(item => item.current !== null && item.current > 0)
})

// 数据分析相关
const isGeneratingPlot = ref(false)
const isAnalyzing = ref(false)
const showDataSummary = ref(false)
const plotImageUrl = ref('')
const analysisResult = ref('')

const summaryData = computed(() => {
  return k1Data.value.map((k1Item, index) => {
    const k01Item = k01Data.value[index]
    return {
      ratio: k1Item.ratio,
      k1Current: (k1Item.current !== null && k1Item.current !== undefined) ? k1Item.current.toFixed(2) : '-',
      k01Current: (k01Item.current !== null && k01Item.current !== undefined) ? k01Item.current.toFixed(2) : '-',
      difference: (k1Item.current !== null && k1Item.current !== undefined &&
                   k01Item.current !== null && k01Item.current !== undefined)
        ? Math.abs(k1Item.current - k01Item.current).toFixed(2)
        : '-'
    }
  })
})

// 方法
const getSubstepStatus = (step: number, substep: number) => {
  if (currentStep.value > step) return 'finish'
  if (currentStep.value === step && currentSubstep.value > substep) return 'finish'
  if (currentStep.value === step && currentSubstep.value === substep) return 'process'
  return 'wait'
}

const nextStep = () => {
  if (currentStep.value < 7) {
    currentStep.value++
    currentSubstep.value = 1
  }
}

const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
    currentSubstep.value = 1
  }
}

const nextSubstep = () => {
  let maxSubsteps = 1
  if (currentStep.value === 1) maxSubsteps = step1Substeps.length
  else if (currentStep.value === 3) maxSubsteps = step3Substeps.length
  else if (currentStep.value === 5) maxSubsteps = step5Substeps.length

  if (currentSubstep.value < maxSubsteps) {
    currentSubstep.value++
  }
}

const prevSubstep = () => {
  if (currentSubstep.value > 1) {
    currentSubstep.value--
  }
}

const validateK1Data = () => {
  // 验证k=1数据的完整性
  console.log('K1 data validation:', k1DataComplete.value)
}

const validateK01Data = () => {
  // 验证k=0.1数据的完整性
  console.log('K0.1 data validation:', k01DataComplete.value)
}

const generatePlot = async () => {
  isGeneratingPlot.value = true
  showDataSummary.value = true

  try {
    // 模拟生成图表的过程
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 这里应该调用后端API生成图表
    // const response = await fetch('/api/generate-plot', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({
    //     k1Data: k1Data.value,
    //     k01Data: k01Data.value
    //   })
    // })
    // const result = await response.json()
    // plotImageUrl.value = result.imageUrl

    // 临时使用占位图
    plotImageUrl.value = '/images/current_control_circuit/sample_plot.png'

    ElMessage.success('图表生成成功！')
  } catch (error) {
    ElMessage.error('图表生成失败，请重试')
  } finally {
    isGeneratingPlot.value = false
  }
}

const analyzeData = async () => {
  isAnalyzing.value = true

  try {
    // 模拟AI分析过程
    await new Promise(resolve => setTimeout(resolve, 3000))

    // 这里应该调用AI分析API
    // const response = await fetch('/api/analyze-data', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({
    //     k1Data: k1Data.value,
    //     k01Data: k01Data.value
    //   })
    // })
    // const result = await response.json()
    // analysisResult.value = result.analysis

    // 临时使用示例分析结果
    analysisResult.value = `
      <div class="analysis-result">
        <h4>实验结果分析：</h4>
        <p><strong>1. 电流变化规律：</strong></p>
        <ul>
          <li>k=1时，电流随接入比例变化相对平缓</li>
          <li>k=0.1时，电流变化更加敏感，体现了恒流电路的特性</li>
        </ul>
        <p><strong>2. 恒流效果：</strong></p>
        <ul>
          <li>当k值较小时(k=0.1)，电路表现出更好的恒流特性</li>
          <li>电流值在不同接入比例下保持相对稳定</li>
        </ul>
        <p><strong>3. 实验结论：</strong></p>
        <ul>
          <li>恒流电路能够在负载变化时保持电流相对稳定</li>
          <li>k值的选择对恒流效果有重要影响</li>
        </ul>
      </div>
    `

    ElMessage.success('数据分析完成！')
  } catch (error) {
    ElMessage.error('数据分析失败，请重试')
  } finally {
    isAnalyzing.value = false
  }
}

const finishExperiment = () => {
  ElMessageBox.confirm(
    '实验已完成，是否返回实验列表？',
    '实验完成',
    {
      confirmButtonText: '返回列表',
      cancelButtonText: '继续查看',
      type: 'success'
    }
  ).then(() => {
    // 返回实验列表
    window.location.href = '/'
  }).catch(() => {
    // 用户选择继续查看
  })
}
</script>

<style scoped>
.current-control-circuit-experiment {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.experiment-header {
  text-align: center;
  margin-bottom: 30px;
}

.experiment-header h1 {
  color: #409eff;
  margin-bottom: 10px;
}

.experiment-description {
  color: #666;
  font-size: 16px;
}

.steps-navigation {
  margin-bottom: 40px;
}

.step-content {
  min-height: 600px;
}

.step-section {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.step-header h2 {
  color: #409eff;
  margin-bottom: 20px;
}

.substeps-nav {
  margin: 20px 0;
}

.substep-content {
  margin: 30px 0;
}

.substep-item h3 {
  color: #333;
  margin-bottom: 15px;
}

.substep-item p {
  margin-bottom: 15px;
  line-height: 1.6;
}

.substep-item ul, .substep-item ol {
  margin-bottom: 20px;
  padding-left: 20px;
}

.substep-item li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.parameter-explanation, .setting-steps, .detailed-instructions {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin: 15px 0;
  border-left: 4px solid #409eff;
}

.parameter-explanation h4, .setting-steps h4, .detailed-instructions h4 {
  color: #409eff;
  margin-bottom: 10px;
}

.detailed-instructions ol {
  counter-reset: step-counter;
}

.detailed-instructions ol > li {
  counter-increment: step-counter;
  margin-bottom: 15px;
  position: relative;
  padding-left: 10px;
}

.detailed-instructions ol > li::before {
  content: counter(step-counter);
  position: absolute;
  left: -25px;
  top: 0;
  background: #409eff;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.detailed-instructions ul {
  margin-top: 8px;
  margin-bottom: 8px;
}

.detailed-instructions ul li {
  margin-bottom: 5px;
}

.circuit-diagram {
  text-align: center;
  margin: 30px 0;
}

.diagram-caption {
  margin-top: 10px;
  color: #666;
  font-style: italic;
}

.connection-checklist {
  margin: 30px 0;
}

.checklist-item {
  margin-bottom: 10px;
}

.progress-container {
  margin: 20px 0;
}

.measurement-instructions {
  background: #f0f9ff;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  border-left: 4px solid #409eff;
}

.measurement-instructions h3 {
  color: #409eff;
  margin-bottom: 15px;
}

.data-table-container {
  margin: 30px 0;
}

.data-table-container h3 {
  color: #333;
  margin-bottom: 15px;
}

.analysis-actions {
  margin: 30px 0;
  text-align: center;
}

.data-summary-container {
  margin: 30px 0;
}

.data-summary-container h3 {
  color: #333;
  margin-bottom: 15px;
}

.plot-container {
  margin: 30px 0;
  text-align: center;
}

.plot-container h3 {
  color: #333;
  margin-bottom: 15px;
}

.analysis-container {
  margin: 30px 0;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #27ae60;
}

.analysis-container h3 {
  color: #27ae60;
  margin-bottom: 15px;
}

.analysis-content {
  line-height: 1.6;
}

.analysis-result h4 {
  color: #333;
  margin: 15px 0 10px 0;
}

.analysis-result ul {
  margin-bottom: 15px;
  padding-left: 20px;
}

.analysis-result li {
  margin-bottom: 5px;
}

.substep-navigation, .step-navigation {
  margin-top: 30px;
  text-align: center;
}

.substep-navigation .el-button, .step-navigation .el-button {
  margin: 0 10px;
}

.success-message {
  margin: 20px 0;
}

.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  transform: translateX(30px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateX(-30px);
  opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
