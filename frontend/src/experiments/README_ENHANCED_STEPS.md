# 增强的实验步骤系统

## 概述

本系统在保持统一Vue模板的基础上，为不同实验提供了独立的、可定制的步骤设置。每个实验可以有自己特定的步骤组件，同时仍然使用统一的`ExperimentTemplate.vue`作为主框架。

## 系统架构

### 1. 统一模板层
- `ExperimentTemplate.vue` - 提供一致的UI框架和导航
- `useSteps.ts` - 统一的步骤管理逻辑
- `useExperimentConfig.ts` - 实验配置管理

### 2. 步骤组件层
#### 通用步骤组件
- `TheoryStep.vue` - 理论原理展示
- `DataInputStep.vue` - 通用数据输入
- `AnalysisStep.vue` - 数据分析和图表
- `CalculationStep.vue` - 计算步骤
- `SummaryStep.vue` - 总结步骤

#### 实验特定步骤组件
- `OscilloscopeOperationStep.vue` - 示波器操作专用步骤
- `DensityMeasurementStep.vue` - 密度测量专用步骤
- 更多实验特定组件...

### 3. 配置层
- JSON配置文件定义每个实验的步骤结构
- 支持灵活的步骤配置和参数设置

## 创建实验特定步骤组件

### 1. 组件结构

```vue
<template>
  <div class="custom-step">
    <!-- 步骤标题 -->
    <div class="step-header">
      <h2>{{ config.title }}</h2>
      <p>{{ config.description }}</p>
    </div>

    <!-- 子步骤导航（可选） -->
    <div v-if="config.subSteps" class="sub-steps-nav">
      <el-steps :active="currentSubStep">
        <el-step 
          v-for="(subStep, index) in config.subSteps" 
          :key="index"
          :title="subStep.title"
        />
      </el-steps>
    </div>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 自定义内容 -->
    </div>

    <!-- 步骤控制 -->
    <div class="step-actions">
      <el-button @click="$emit('prev')">上一步</el-button>
      <el-button type="primary" @click="$emit('next')">下一步</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  config: any
  data: any
}

interface Emits {
  (e: 'update', data: any): void
  (e: 'next'): void
  (e: 'prev'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 组件逻辑
</script>
```

### 2. 配置文件结构

```json
{
  "info": {
    "id": "experiment_id",
    "name": "实验名称",
    "description": "实验描述"
  },
  "steps": [
    {
      "id": "custom_step",
      "title": "自定义步骤",
      "description": "步骤描述",
      "component": "CustomStepComponent",
      "config": {
        "title": "步骤标题",
        "description": "详细描述",
        "subSteps": [
          {
            "title": "子步骤1",
            "description": "子步骤描述",
            "instructions": [
              {
                "text": "操作指令",
                "checkable": true,
                "required": true
              }
            ]
          }
        ]
      }
    }
  ]
}
```

## 示例：示波器操作步骤

### 特点
- **多层级步骤导航** - 支持主步骤下的子步骤
- **交互式指令清单** - 可勾选的操作指令
- **参数设置界面** - 实时参数输入和验证
- **进度跟踪** - 自动跟踪完成状态

### 使用方法
1. 在配置文件中指定 `"component": "OscilloscopeOperationStep"`
2. 在config中定义subSteps数组
3. 每个子步骤可包含instructions、parameters、hints等

```json
{
  "component": "OscilloscopeOperationStep",
  "config": {
    "subSteps": [
      {
        "title": "设备检查",
        "instructions": [
          {
            "text": "检查示波器电源线",
            "checkable": true,
            "required": true
          }
        ]
      }
    ]
  }
}
```

## 示例：密度测量步骤

### 特点
- **多测量类型支持** - 质量、体积等不同测量
- **数据表格编辑** - 直接在表格中编辑数据
- **实时统计计算** - 自动计算平均值、标准偏差
- **测量指导** - 分步骤的测量指导

### 使用方法
1. 在配置文件中指定 `"component": "DensityMeasurementStep"`
2. 定义measurementTypes数组
3. 每个测量类型包含数据表格配置

```json
{
  "component": "DensityMeasurementStep",
  "config": {
    "measurementTypes": [
      {
        "id": "mass",
        "name": "质量测量",
        "dataTable": {
          "columns": [
            {
              "prop": "measurement_1",
              "label": "第1次测量",
              "precision": 2
            }
          ]
        }
      }
    ]
  }
}
```

## 扩展指南

### 1. 创建新的实验特定组件
1. 在 `frontend/src/components/steps/` 目录下创建新组件
2. 实现标准的Props和Emits接口
3. 在 `ExperimentTemplate.vue` 的 `componentMap` 中注册组件

### 2. 配置文件最佳实践
- 使用清晰的步骤ID和组件名称
- 提供详细的配置说明和提示
- 支持验证规则和错误处理
- 考虑步骤间的数据依赖关系

### 3. 数据管理
- 使用统一的数据更新机制（emit('update')）
- 支持步骤数据的持久化和恢复
- 实现数据验证和错误处理

## 优势

1. **保持UI一致性** - 所有实验使用统一的模板框架
2. **高度可定制** - 每个实验可以有独特的步骤实现
3. **可复用性** - 通用组件可以被多个实验复用
4. **易于维护** - 清晰的组件分层和配置管理
5. **扩展性强** - 容易添加新的实验和步骤类型

## 总结

这个增强的步骤系统在保持统一Vue模板的同时，为每个实验提供了独立的、详细的步骤设置。通过实验特定的步骤组件，可以更好地体现每个实验的独特性和复杂性，同时保持系统的整体一致性和可维护性。
