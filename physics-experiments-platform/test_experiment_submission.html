<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实验提交功能测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-item { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .status { margin-top: 10px; padding: 10px; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>实验提交功能测试</h1>
    
    <div id="app">
        <div class="test-item">
            <h3>Vue组件加载测试</h3>
            <div class="status info">
                <p>ExperimentNavigationComponent: {{ typeof ExperimentNavigationComponent }}</p>
                <p>ExperimentSubmissionModalComponent: {{ typeof ExperimentSubmissionModalComponent }}</p>
            </div>
        </div>

        <div class="test-item">
            <h3>提交模态框测试</h3>
            <button class="test-button" @click="showModal = true">显示提交模态框</button>
            <div v-if="showModal" class="status success">模态框已显示</div>
            
            <experiment-submission-modal
                experiment-type="test"
                :visible="showModal"
                @submit="handleSubmit"
                @cancel="handleCancel"
                @success="handleSuccess">
            </experiment-submission-modal>
        </div>

        <div class="test-item">
            <h3>导航组件测试</h3>
            <experiment-navigation-component
                :current-step="1"
                :current-substep="1"
                :total-substeps="1"
                :show-prev="false"
                :show-next="true"
                next-text="测试完成实验"
                @next="testFinishExperiment">
            </experiment-navigation-component>
        </div>

        <div class="test-item">
            <h3>测试结果</h3>
            <div v-for="result in testResults" :key="result.id" 
                 :class="['status', result.type]">
                {{ result.message }}
            </div>
        </div>
    </div>

    <script src="app/static/js/vue-components.js"></script>
    <script>
        const { createApp } = Vue;

        const app = createApp({
            data() {
                return {
                    showModal: false,
                    testResults: [],
                    ExperimentNavigationComponent: typeof window.ExperimentNavigationComponent,
                    ExperimentSubmissionModalComponent: typeof window.ExperimentSubmissionModalComponent
                };
            },
            components: {
                'experiment-navigation-component': window.ExperimentNavigationComponent,
                'experiment-submission-modal': window.ExperimentSubmissionModalComponent
            },
            methods: {
                addResult(message, type = 'info') {
                    this.testResults.push({
                        id: Date.now(),
                        message,
                        type
                    });
                },
                handleSubmit(data) {
                    this.addResult(`提交测试成功: ${data.studentId} - ${data.studentName}`, 'success');
                    setTimeout(() => {
                        this.$refs.submissionModal?.showSuccess('测试提交成功！');
                    }, 1000);
                },
                handleCancel() {
                    this.showModal = false;
                    this.addResult('用户取消提交', 'info');
                },
                handleSuccess() {
                    this.showModal = false;
                    this.addResult('提交流程完成', 'success');
                },
                testFinishExperiment() {
                    this.addResult('完成实验按钮点击测试成功', 'success');
                    this.showModal = true;
                }
            },
            mounted() {
                this.addResult('Vue应用初始化成功', 'success');
                
                // 测试组件是否正确加载
                if (typeof window.ExperimentNavigationComponent === 'undefined') {
                    this.addResult('ExperimentNavigationComponent 未加载', 'error');
                } else {
                    this.addResult('ExperimentNavigationComponent 加载成功', 'success');
                }
                
                if (typeof window.ExperimentSubmissionModalComponent === 'undefined') {
                    this.addResult('ExperimentSubmissionModalComponent 未加载', 'error');
                } else {
                    this.addResult('ExperimentSubmissionModalComponent 加载成功', 'success');
                }
            }
        });

        app.mount('#app');
    </script>
</body>
</html>
